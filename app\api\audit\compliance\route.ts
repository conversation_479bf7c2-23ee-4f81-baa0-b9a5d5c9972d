/**
 * Compliance Report API Route
 * Generate compliance reports for Enterprise customers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { generateComplianceReport } from '@/lib/audit-logging';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/audit/compliance
 * Generate compliance report
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Compliance reports require Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Compliance reports require Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      const organizationId = searchParams.get('organization_id') || user.id;
      const startDate = searchParams.get('start_date');
      const endDate = searchParams.get('end_date');

      if (!startDate || !endDate) {
        return NextResponse.json(
          { error: 'start_date and end_date are required' },
          { status: 400 }
        );
      }

      const result = await generateComplianceReport(organizationId, startDate, endDate);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        report: result.report,
      });

    } catch (error) {
      console.error('Compliance report API error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to generate compliance report' },
        { status: 500 }
      );
    }
  });
}
