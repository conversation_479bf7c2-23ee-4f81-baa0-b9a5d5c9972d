/**
 * Audit Export API Route
 * Export audit logs for compliance
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { exportAuditLogs, type AuditFilters } from '@/lib/audit-logging';
import { withSubscriptionCheck } from '@/lib/subscription-middleware';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/audit/export
 * Export audit logs
 */
export async function GET(request: NextRequest) {
  return withSubscriptionCheck(request, async (req, context) => {
    try {
      // Get authenticated user
      const user = await getCurrentUser();
      if (!user) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Audit export requires Enterprise subscription
      if (context.subscription.tier !== 'ENTERPRISE') {
        return NextResponse.json(
          {
            error: 'Audit export requires Enterprise subscription',
            upgrade_required: true,
            current_tier: context.subscription.tier,
          },
          { status: 403 }
        );
      }

      const { searchParams } = new URL(req.url);
      const format = (searchParams.get('format') || 'json') as 'csv' | 'json';
      
      if (!['csv', 'json'].includes(format)) {
        return NextResponse.json(
          { error: 'Invalid format. Must be csv or json' },
          { status: 400 }
        );
      }

      const filters: AuditFilters = {
        organization_id: searchParams.get('organization_id') || user.id,
        user_id: searchParams.get('user_id') || undefined,
        action: searchParams.get('action') || undefined,
        resource_type: searchParams.get('resource_type') || undefined,
        risk_level: (searchParams.get('risk_level') as any) || undefined,
        status: (searchParams.get('status') as any) || undefined,
        start_date: searchParams.get('start_date') || undefined,
        end_date: searchParams.get('end_date') || undefined,
      };

      const result = await exportAuditLogs(filters, format);

      if (!result.success) {
        return NextResponse.json(
          { error: result.error },
          { status: 500 }
        );
      }

      // Set appropriate headers for file download
      const headers = new Headers();
      
      if (format === 'csv') {
        headers.set('Content-Type', 'text/csv');
      } else {
        headers.set('Content-Type', 'application/json');
      }
      
      headers.set('Content-Disposition', `attachment; filename="${result.filename}"`);

      return new NextResponse(result.data, {
        status: 200,
        headers,
      });

    } catch (error) {
      console.error('Audit export error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      return NextResponse.json(
        { error: 'Failed to export audit logs' },
        { status: 500 }
      );
    }
  });
}
