/**
 * CRM Integrations Management API
 * Manages user's CRM integrations and sync operations
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getUserCRMConnections,
  pushSummaryToCRM,
} from '@/lib/crm-integrations';
// Removed Supabase import - using dev-only mode
import { getCurrentUser } from '@/lib/user-management';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/crm/integrations
 * Get user's CRM integrations
 */
export async function GET(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id') || `org-${userId}`;

    const integrations = await getUserCRMConnections(userId, organizationId);

    // Remove sensitive data before sending to client
    const safeIntegrations = integrations.map(integration => ({
      id: integration.id,
      integration_type: integration.crm_type,
      workspace_name: integration.crm_account_id,
      workspace_id: integration.crm_user_id,
      is_active: integration.is_active,
      settings: {
        auto_sync: integration.settings?.auto_sync || false,
        sync_frequency: integration.settings?.sync_frequency || 'daily',
        tag_mapping: integration.settings?.tag_mapping || {},
      },
      created_at: integration.created_at,
      updated_at: integration.updated_at,
    }));

    return NextResponse.json({
      success: true,
      integrations: safeIntegrations,
    });

  } catch (error) {
    console.error('Error fetching CRM integrations:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to fetch CRM integrations' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/crm/integrations
 * Update CRM integration settings
 */
export async function PUT(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    const body = await request.json();
    const { integration_id, settings, is_active } = body;

    if (!integration_id) {
      return NextResponse.json(
        { error: 'Integration ID is required' },
        { status: 400 }
      );
    }

    // DEV MODE: Mock integration update
    const updates: any = {};
    if (settings !== undefined) updates.settings = settings;
    if (is_active !== undefined) updates.is_active = is_active;
    updates.updated_at = new Date().toISOString();

    console.log('📄 Dev mode: Skipping CRM integration update');
    const data = {
      id: integration_id,
      user_id: userId,
      crm_type: 'hubspot',
      crm_account_id: 'demo-account',
      is_active: is_active !== undefined ? is_active : true,
      settings: settings || {},
      ...updates
    };

    return NextResponse.json({
      success: true,
      integration: {
        id: data.id,
        integration_type: data.crm_type,
        workspace_name: data.crm_account_id,
        is_active: data.is_active,
        settings: data.settings,
        updated_at: data.updated_at,
      },
      message: 'Integration settings updated successfully',
    });

  } catch (error) {
    console.error('Error updating CRM integration:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to update CRM integration' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/crm/integrations
 * Remove CRM integration
 */
export async function DELETE(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    const { searchParams } = new URL(request.url);
    const integrationId = searchParams.get('integration_id');

    if (!integrationId) {
      return NextResponse.json(
        { error: 'Integration ID is required' },
        { status: 400 }
      );
    }

    // DEV MODE: Mock integration deletion
    console.log('📄 Dev mode: Skipping CRM integration deletion');

    return NextResponse.json({
      success: true,
      message: 'CRM integration removed successfully',
    });

  } catch (error) {
    console.error('Error removing CRM integration:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to remove CRM integration' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/crm/integrations
 * Sync summary to CRM
 */
export async function POST(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;

    const body = await request.json();
    const { summary_id, integration_id, metadata } = body;

    if (!summary_id || !integration_id) {
      return NextResponse.json(
        { error: 'Summary ID and Integration ID are required' },
        { status: 400 }
      );
    }

    // DEV MODE: Mock integration verification
    const integration = {
      id: integration_id,
      user_id: userId,
      crm_type: 'hubspot',
      is_active: true,
      settings: {}
    };

    console.log('📄 Dev mode: Using mock CRM integration');

    // Sync to CRM
    const organizationId = `org-${userId}`;
    const result = await pushSummaryToCRM(summary_id, userId, organizationId, integration.crm_type as any);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    // DEV MODE: Mock sync operation logging
    console.log('📄 Dev mode: Skipping CRM sync log insertion');

    return NextResponse.json({
      success: true,
      external_id: result.crm_record_id,
      external_url: null,
      message: `Summary synced to ${integration.crm_type} successfully`,
    });

  } catch (error) {
    console.error('Error syncing to CRM:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to sync to CRM' },
      { status: 500 }
    );
  }
}
