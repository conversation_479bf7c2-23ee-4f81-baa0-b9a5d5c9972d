/**
 * CRM OAuth Callback Route
 * Handles OAuth callbacks from Notion and HubSpot
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  exchangeCRMOAuthCode,
  storeCRMIntegration,
} from '@/lib/crm-integrations';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/crm/oauth/callback
 * Handle OAuth callback from CRM providers
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    // Handle OAuth errors
    if (error) {
      const errorDescription = searchParams.get('error_description') || 'OAuth authorization failed';
      console.error('OAuth error:', error, errorDescription);
      
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=${encodeURIComponent(errorDescription)}`
      );
    }

    if (!code || !state) {
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=Missing authorization code or state`
      );
    }

    try {
      // Decode state to get user info
      const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
      const { userId, organizationId, integrationType } = stateData;

      if (!userId || !organizationId || !integrationType) {
        throw new Error('Invalid state data');
      }

      // Exchange code for tokens
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
      const redirectUri = `${baseUrl}/api/crm/oauth/callback`;

      const tokenData = await exchangeCRMOAuthCode(integrationType, code, redirectUri);

      // Store integration
      const integration = await storeCRMIntegration(userId, organizationId, integrationType, tokenData);

      // Redirect to settings page with success message
      const successMessage = `${integrationType} integration completed successfully`;
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?success=${encodeURIComponent(successMessage)}&integration=${integrationType}`
      );

    } catch (error) {
      console.error('OAuth callback processing error:', error);
      SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete integration';
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=${encodeURIComponent(errorMessage)}`
      );
    }

  } catch (error) {
    console.error('OAuth callback error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=OAuth callback failed`
    );
  }
}
