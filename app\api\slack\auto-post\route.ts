import { NextRequest, NextResponse } from 'next/server';
import { autoPostSummaryToSlack, retryFailedSlackPosts } from '@/lib/slack-auto-post';
import { SentryTracker } from '@/lib/sentry.client';
import { getCurrentUser } from '@/lib/user-management';
import { createSupabaseServerClient } from '@/lib/supabase-server';

/**
 * POST /api/slack/auto-post
 * Manually trigger auto-post for a specific summary
 */
export async function POST(request: NextRequest) {
  try {
    const { summary_id, organization_id } = await request.json();

    if (!summary_id) {
      return NextResponse.json(
        { error: 'Summary ID is required' },
        { status: 400 }
      );
    }

    // Production demo mode - simulate Slack auto-post
    console.log('📄 Slack Auto-Post API called - Production Demo Mode:', summary_id);

    // Check if this is a demo summary
    if (!summary_id.startsWith('demo-summary-')) {
      return NextResponse.json(
        { error: 'Summary not found' },
        { status: 404 }
      );
    }

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Use user ID as organization ID
    const orgId = organization_id || user.id;

    // Simulate Slack posting for demo mode
    console.log('📄 Simulating Slack auto-post for demo summary:', summary_id);

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return successful demo result
    return NextResponse.json({
      success: true,
      message: 'Summary posted to Slack successfully (Demo Mode)',
      data: {
        message_ts: `demo-${Date.now()}.${Math.random().toString(36).substr(2, 9)}`,
        summary_id,
        channel: '#general',
        workspace: 'Acme Corporation Demo'
      }
    });

  } catch (error) {
    console.error('Slack auto-post API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/slack/auto-post
 * Get auto-post status and history for user
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Production demo mode - return demo auto-post data
    console.log('📄 Get Slack Auto-Post Status API called - Production Demo Mode');

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const orgId = organizationId || user.id;

    // Return demo auto-post data
    const demoSettings = {
      auto_post_enabled: true,
      channel_preference: 'same_channel'
    };

    const demoStatistics = {
      total_posts: 12,
      successful_posts: 10,
      failed_posts: 1,
      pending_posts: 1
    };

    const demoRecentPosts = [
      {
        id: 'demo-post-1',
        summary_id: 'demo-summary-1-12345678-1234-1234-1234-123456789012',
        status: 'posted',
        channel: '#engineering',
        message_ts: '1703123456.789012',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        summaries: {
          title: 'Q4 Engineering Sprint Planning',
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        }
      },
      {
        id: 'demo-post-2',
        summary_id: 'demo-summary-2-12345678-1234-1234-1234-123456789012',
        status: 'posted',
        channel: '#product',
        message_ts: '1703109876.543210',
        created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        summaries: {
          title: 'Product Roadmap Review - Mobile App Features',
          created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
        }
      }
    ].slice(0, limit);

    return NextResponse.json({
      success: true,
      data: {
        settings: demoSettings,
        statistics: demoStatistics,
        recent_posts: demoRecentPosts
      }
    });

  } catch (error) {
    console.error('Get Slack auto-post status API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/slack/auto-post
 * Update auto-post settings
 */
export async function PUT(request: NextRequest) {
  try {
    const { 
      auto_post_enabled, 
      channel_preference,
      organization_id 
    } = await request.json();

    // Production demo mode - simulate settings update
    console.log('📄 Update Slack Auto-Post Settings API called - Production Demo Mode');

    // Get authenticated user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const orgId = organization_id || user.id;

    // Validate channel preference
    if (channel_preference && !['same_channel', 'dm_user'].includes(channel_preference)) {
      return NextResponse.json(
        { error: 'Invalid channel preference. Must be "same_channel" or "dm_user"' },
        { status: 400 }
      );
    }

    // Simulate settings update for demo mode
    console.log('📄 Demo settings updated:', { auto_post_enabled, channel_preference, orgId });

    return NextResponse.json({
      success: true,
      message: 'Auto-post settings updated successfully (Demo Mode)',
      data: {
        auto_post_enabled: auto_post_enabled,
        channel_preference: channel_preference || 'same_channel'
      }
    });

  } catch (error) {
    console.error('Update Slack auto-post settings API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/slack/auto-post
 * Retry failed posts
 */
export async function PATCH(request: NextRequest) {
  try {
    // Production demo mode - simulate retry
    console.log('📄 Retry Failed Slack Posts API called - Production Demo Mode');

    // Simulate retry processing time
    await new Promise(resolve => setTimeout(resolve, 500));

    return NextResponse.json({
      success: true,
      message: 'Failed posts retry initiated (Demo Mode)',
      data: {
        retried_posts: 1,
        successful_retries: 1,
        failed_retries: 0
      }
    });

  } catch (error) {
    console.error('Retry failed Slack posts API error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
