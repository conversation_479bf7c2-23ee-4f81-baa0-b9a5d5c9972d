/**
 * Slack Channels API Route
 * Fetches available Slack channels for scheduling
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { getUserSlackChannels } from '@/lib/slack-scheduler';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/slack/channels
 * Get user's available Slack channels
 */
export async function GET(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id') || `org-${userId}`;

    const channels = await getUserSlackChannels(userId, organizationId);

    return NextResponse.json({
      success: true,
      channels,
    });

  } catch (error) {
    console.error('Error fetching Slack channels:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch Slack channels',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
