/**
 * Slack Scheduler API Routes
 * Manages scheduled Slack posts
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import {
  createScheduledPost,
  getUserScheduledPosts,
  updateScheduledPost,
  deleteScheduledPost,
  getUserSlackChannels,
} from '@/lib/slack-scheduler';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * GET /api/slack/scheduler
 * Get user's scheduled posts
 */
export async function GET(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id') || `org-${userId}`;

    const scheduledPosts = await getUserScheduledPosts(userId, organizationId);

    return NextResponse.json({
      success: true,
      scheduled_posts: scheduledPosts,
    });

  } catch (error) {
    console.error('Error fetching scheduled posts:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to fetch scheduled posts' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/slack/scheduler
 * Create a new scheduled post
 */
export async function POST(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const body = await request.json();
    const {
      organization_id,
      slack_channel_id,
      slack_channel_name,
      template_id,
      schedule_type,
      schedule_time,
      schedule_day,
      timezone,
      is_active = true,
    } = body;

    // Validate required fields
    if (!slack_channel_id || !template_id || !schedule_type || !schedule_time) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate schedule type
    if (!['daily', 'weekly', 'monthly'].includes(schedule_type)) {
      return NextResponse.json(
        { error: 'Invalid schedule type' },
        { status: 400 }
      );
    }

    // Validate schedule time format (HH:MM)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(schedule_time)) {
      return NextResponse.json(
        { error: 'Invalid time format. Use HH:MM' },
        { status: 400 }
      );
    }

    // Validate schedule day for weekly/monthly
    if (schedule_type === 'weekly' && (schedule_day < 0 || schedule_day > 6)) {
      return NextResponse.json(
        { error: 'Invalid day for weekly schedule (0-6)' },
        { status: 400 }
      );
    }

    if (schedule_type === 'monthly' && (schedule_day < 1 || schedule_day > 31)) {
      return NextResponse.json(
        { error: 'Invalid day for monthly schedule (1-31)' },
        { status: 400 }
      );
    }

    const orgId = organization_id || `org-${userId}`;

    const result = await createScheduledPost(userId, orgId, {
      slack_channel_id,
      slack_channel_name,
      template_id,
      schedule_type,
      schedule_time,
      schedule_day,
      timezone: timezone || 'UTC',
      is_active,
      next_post_at: '', // Will be calculated in the service
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      scheduled_post: result.scheduledPost,
    });

  } catch (error) {
    console.error('Error creating scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to create scheduled post' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/slack/scheduler
 * Update a scheduled post
 */
export async function PUT(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const body = await request.json();
    const { post_id, ...updates } = body;

    if (!post_id) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    const result = await updateScheduledPost(post_id, userId, updates);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Scheduled post updated successfully',
    });

  } catch (error) {
    console.error('Error updating scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to update scheduled post' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/slack/scheduler
 * Delete a scheduled post
 */
export async function DELETE(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const { searchParams } = new URL(request.url);
    const postId = searchParams.get('post_id');

    if (!postId) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    const result = await deleteScheduledPost(postId, userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Scheduled post deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting scheduled post:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to delete scheduled post' },
      { status: 500 }
    );
  }
}
