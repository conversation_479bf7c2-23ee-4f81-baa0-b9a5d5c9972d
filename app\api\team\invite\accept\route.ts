/**
 * Team Invitation Acceptance API Route
 * Handle team invitation acceptance
 */

import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/user-management';
import { acceptTeamInvitation } from '@/lib/team-management';
import { SentryTracker } from '@/lib/sentry.client';

/**
 * POST /api/team/invite/accept
 * Accept team invitation
 */
export async function POST(request: NextRequest) {
  try {
    // Public Demo Mode - no authentication required
    const user = await getCurrentUser();
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }
  const userId = user.id;
    

    const body = await request.json();
    const { invitation_token } = body;

    if (!invitation_token) {
      return NextResponse.json(
        { error: 'Invitation token is required' },
        { status: 400 }
      );
    }

    const result = await acceptTeamInvitation(invitation_token, userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      membership: result.membership,
      message: 'Team invitation accepted successfully',
    });

  } catch (error) {
    console.error('Team invitation acceptance error:', error);
    SentryTracker.captureException(error instanceof Error ? error : new Error(String(error)));
    
    return NextResponse.json(
      { error: 'Failed to accept team invitation' },
      { status: 500 }
    );
  }
}
