'use client';

/**
 * Authentication Guard Component
 * 
 * Protects routes by checking user authentication status
 * Redirects unauthenticated users to login page
 */

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDevAuth } from '@/lib/dev-auth';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

export default function AuthGuard({
  children,
  redirectTo = '/auth/login',
  fallback
}: AuthGuardProps) {
  const { user, isLoading } = useDevAuth();

  // DEV MODE: Always allow access since we're using dev auth
  // In dev mode, user is always authenticated

  // Show loading state while dev auth is initializing
  if (isLoading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading dev environment...</p>
        </div>
      </div>
    );
  }

  // In dev mode, always render children (no authentication required)
  return <>{children}</>;
}

/**
 * Hook to get current authenticated user - DEV MODE
 */
export function useAuthUser() {
  const { user, isLoading } = useDevAuth();

  return {
    user,
    loading: isLoading,
    error: null // No errors in dev mode
  };
}
