'use client';

/**
 * DEV PROVIDERS FOR NEXT.JS 15 APP ROUTER (NO AUTH)
 *
 * FEATURES:
 * ✅ Dev-only mock authentication
 * ✅ Theme provider for dark/light mode
 * ✅ Toast notifications
 * ✅ Error boundaries
 * ✅ Analytics tracking (optional)
 * ✅ Chunk loading error recovery
 * ✅ Development-ready
 */

import React, { useEffect, useState } from 'react';
import { Toaster } from '@/components/ui/sonner';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ThemeProvider } from '@/components/theme-provider';


interface ProvidersProps {
  children: React.ReactNode;
}

/**
 * Enhanced chunk loading error recovery utility
 */
function handleChunkLoadError() {
  if (typeof window !== 'undefined') {
    console.warn('🔄 ChunkLoadError detected - attempting recovery...');

    // Clear service workers
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        registrations.forEach(registration => registration.unregister());
      });
    }

    // Clear all caches
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name));
      });
    }

    // Clear localStorage chunk cache
    Object.keys(localStorage).forEach(key => {
      if (key.includes('chunk') || key.includes('webpack')) {
        localStorage.removeItem(key);
      }
    });

    // Reload with cache bypass
    window.location.reload();
  }
}

/**
 * Dev Providers component with enhanced chunk loading error recovery
 * Provides all necessary context providers for development (no auth required)
 */
export function Providers({ children }: ProvidersProps) {
  const [isClient, setIsClient] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;

  useEffect(() => {
    setIsClient(true);

    // Enhanced chunk loading error handler with retry logic
    const handleError = (event: ErrorEvent) => {
      const isChunkError = event.message?.includes('ChunkLoadError') ||
                          event.message?.includes('Loading chunk') ||
                          event.message?.includes('Loading CSS chunk') ||
                          event.message?.includes('script error');

      if (isChunkError) {
        console.warn(`🔄 Chunk loading error detected (attempt ${retryCount + 1}/${maxRetries})`);

        if (retryCount < maxRetries) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => {
            handleChunkLoadError();
          }, 1000 * (retryCount + 1)); // Exponential backoff
        } else {
          console.error('❌ Max chunk loading retries exceeded');
          // Show user-friendly error message
          if (confirm('The application encountered loading issues. Would you like to refresh the page?')) {
            window.location.reload();
          }
        }
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const isChunkError = event.reason?.name === 'ChunkLoadError' ||
                          (event.reason?.message && event.reason.message.includes('Loading chunk'));

      if (isChunkError) {
        console.warn(`🔄 Chunk loading promise rejection detected (attempt ${retryCount + 1}/${maxRetries})`);
        event.preventDefault();

        if (retryCount < maxRetries) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => {
            handleChunkLoadError();
          }, 1000 * (retryCount + 1));
        }
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [retryCount]);

  // Prevent hydration mismatch by only rendering client-side content after hydration
  if (!isClient) {
    return (
      <div suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading application...</p>
            </div>
          </div>
        </ThemeProvider>
      </div>
    );
  }

  return (
    <div suppressHydrationWarning>
      <ErrorBoundary>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </ErrorBoundary>
    </div>
  );
}
